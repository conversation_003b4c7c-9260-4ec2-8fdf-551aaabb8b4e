{"sections": [{"title": "Design (Layout)", "totalPrice": 400, "items": [{"no": 1, "level1": "Total", "level2": "Adapt global styles to local styles", "level3": "", "level4": "", "hours": 32, "time": 4.0, "price": 400, "note": ""}]}, {"title": "Zalo Mini App", "totalPrice": 775, "items": [{"no": 2, "level1": "<PERSON><PERSON><PERSON>", "level2": "SignIn simple", "level3": "", "level4": "", "hours": 2, "time": 0.25, "price": 25, "note": ""}, {"no": 3, "level1": "Notifications", "level2": "Firebase notifications or Expo notifications", "level3": "", "level4": "", "hours": 24, "time": 3.0, "price": 300, "note": ""}, {"no": 4, "level1": "Responsive", "level2": "- Mobile", "level3": "", "level4": "", "hours": 36, "time": 4.5, "price": 450, "note": ""}]}, {"title": "Admin Dashboard", "totalPrice": 1021, "items": [{"no": 5, "level1": "<PERSON><PERSON><PERSON>", "level2": "SignIn/SignUp by Email/Username", "level3": "", "level4": "", "hours": 8, "time": 1, "price": 100, "note": ""}, {"no": 6, "level1": "User managements", "level2": "CRUD (Create Update Delete + List)", "level3": "", "level4": "", "hours": 8, "time": 1, "price": 100, "note": ""}, {"no": 7, "level1": "User managements", "level2": "User Roles: <PERSON><PERSON>, Users", "level3": "", "level4": "", "hours": 24, "time": 3, "price": 250, "note": ""}, {"no": 8, "level1": "Data management", "level2": "Date, images reviewing", "level3": "", "level4": "", "hours": 40, "time": 5, "price": 500, "note": ""}, {"no": 9, "level1": "Report", "level2": "", "level3": "", "level4": "", "hours": 0, "time": 0, "price": 0, "note": "N/A"}, {"no": 10, "level1": "Responsive", "level2": "- Desktop", "level3": "", "level4": "", "hours": 5.68, "time": 0.71, "price": 71, "note": ""}]}, {"title": "QC (Testing)", "totalPrice": 100, "items": [{"no": 11, "level1": "Testing", "level2": "- Web browser\n    Edge: 80+\n    Chrome: 80+", "level3": "", "level4": "", "hours": 8, "time": 1, "price": 100, "note": ""}]}], "grandTotal": 1896}