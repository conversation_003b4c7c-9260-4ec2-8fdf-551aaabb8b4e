{"project": {"title": "Project Estimation", "grandTotal": 0, "sections": {"admin_dashboard": {"id": "admin_dashboard", "title": "Admin Dashboard", "level1": {"authentication": {"id": "authentication", "name": "<PERSON><PERSON><PERSON>", "level2": {"signin_signup": {"id": "signin_signup", "name": "SignIn/SignUp by Email/Username", "hours": 8}}}, "user_management": {"id": "user_management", "name": "User managements", "level2": {"crud_operations": {"id": "crud_operations", "name": "CRUD (Create Update Delete + List)", "hours": 8}, "user_roles": {"id": "user_roles", "name": "User Roles: <PERSON><PERSON>, Users", "hours": 24}}}, "data_management": {"id": "data_management", "name": "Data management", "level2": {"data_images_reviewing": {"id": "data_images_reviewing", "name": "Date, images reviewing", "hours": 40}}}, "report": {"id": "report", "name": "Report", "level2": {"not_applicable": {"id": "not_applicable", "name": "", "hours": 0}}}, "responsive": {"id": "responsive", "name": "Responsive", "level2": {"desktop": {"id": "desktop", "name": "- Desktop", "hours": 5.68}}}}}, "qc_testing": {"id": "qc_testing", "title": "Testing", "level1": {"testing": {"id": "testing", "name": "Testing", "level2": {"web_browser_testing": {"id": "web_browser_testing", "name": "- Web browser\n    Edge: 80+\n    Chrome: 80+", "hours": 8}}}}}}}}