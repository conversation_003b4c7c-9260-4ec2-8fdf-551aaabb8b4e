import sectionData from '@/data/section.json';

// Type definitions for the section data structure
interface Level2Item {
  id: string;
  name: string;
  hours: number;
}

interface Level1Item {
  id: string;
  name: string;
  level2: Record<string, Level2Item>;
}

interface Section {
  id: string;
  title: string;
  level1: Record<string, Level1Item>;
}

interface ProjectData {
  project: {
    title: string;
    grandTotal: number;
    sections: Record<string, Section>;
  };
}

export const convertSectionToData = () => {
  try {
    const typedSectionData = sectionData as ProjectData;
    const sections = [];
    let grandTotal = 0;

    let number = 0;

    // Iterate through each section in the project
    for (const [sectionKey, sectionValue] of Object.entries(typedSectionData.project.sections)) {
      const sectionItems = [];
      let sectionTotalPrice = 0;

      // Validate section structure
      if (!sectionValue.level1) {
        console.warn(`Section ${sectionKey} is missing level1`);
        continue;
      }

      // Iterate through level1 items in each section
      for (const [level1Key, level1Value] of Object.entries(sectionValue.level1)) {
        // Validate level1 structure
        if (!level1Value.level2) {
          console.warn(`Level1 ${level1Key} in section ${sectionKey} is missing level2`);
          continue;
        }

        // Iterate through level2 items in each level1
        for (const [level2Key, level2Value] of Object.entries(level1Value.level2)) {
          // Validate level2 structure
          if (typeof level2Value.hours !== 'number') {
            console.warn(`Level2 ${level2Key} in level1 ${level1Key} has invalid hours`);
            continue;
          }

          // Calculate time (in days) and price based on hours
          const hours = level2Value.hours;
          const time = hours / 8; // 1 day = 8 hours
          const price = hours * 12.5; // Price = hours * 12.5 USD

          number = number + 1;

          // Add level2 data to sectionItems
          sectionItems.push({
            no: number,
            level1: level1Value.name || '',
            level2: level2Value.name || '',
            level3: '',
            level4: '',
            hours: level2Value.hours,
            time: time,
            price: price,
            note: ''
          });

          // Add level2 price to section total
          sectionTotalPrice += price;
        }
      }

      // Add section data to sections array
      sections.push({
        title: sectionValue.title || sectionKey,
        totalPrice: sectionTotalPrice,
        items: sectionItems
      });

      // Add section total to grand total
      grandTotal += sectionTotalPrice;
    }

    return {
      sections,
      grandTotal
    };
  } catch (error) {
    console.error('Error processing section.json:', error);
    throw new Error('Failed to convert section data due to invalid JSON or structure');
  }
};