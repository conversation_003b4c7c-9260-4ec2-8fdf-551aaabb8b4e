import sectionData from '@/data/section.json';

// Type definitions for the multi-level hierarchical structure
interface HierarchyNode {
  id: string;
  name: string;
  hours: number | null;
  children: HierarchyNode[] | null;
}

interface ProjectData {
  project: {
    title: string;
    grandTotal: number;
    sections: Record<string, HierarchyNode>;
  };
}

export const convertSectionToData = () => {
  try {
    const typedSectionData = sectionData as ProjectData;
    const sections = [];
    let grandTotal = 0;
    let number = 0;

    // Helper function to recursively traverse the hierarchy and collect leaf nodes with hours
    const collectLeafNodes = (node: HierarchyNode, level1Name = '', level2Name = '', level3Name = '', level4Name = ''): any[] => {
      const items = [];

      if (node.children && node.children.length > 0) {
        // This is not a leaf node, traverse children
        for (const child of node.children) {
          const childItems = collectLeafNodes(
            child,
            level1Name || node.name,
            level1Name ? (level2Name || node.name) : level2Name,
            level2Name ? (level3Name || node.name) : level3Name,
            level3Name ? (level4Name || node.name) : level4Name
          );
          items.push(...childItems);
        }
      } else if (node.hours !== null && node.hours !== undefined) {
        // This is a leaf node with hours, create an item
        number = number + 1;

        const hours = node.hours;
        const time = hours / 8; // 1 day = 8 hours
        const price = hours * 12.5; // Price = hours * 12.5 USD

        items.push({
          no: number,
          level1: level1Name || node.name,
          level2: level2Name || node.name,
          level3: level3Name,
          level4: level4Name,
          hours: hours,
          time: time,
          price: price,
          note: ''
        });
      }

      return items;
    };

    // Process each section in the project.sections
    for (const [, sectionValue] of Object.entries(typedSectionData.project.sections)) {
      const sectionItems = collectLeafNodes(sectionValue);
      let sectionTotalPrice = 0;

      // Calculate section total
      for (const item of sectionItems) {
        sectionTotalPrice += item.price;
      }

      // Add section data to sections array
      sections.push({
        title: sectionValue.name,
        totalPrice: sectionTotalPrice,
        items: sectionItems
      });

      // Add section total to grand total
      grandTotal += sectionTotalPrice;
    }

    return {
      sections,
      grandTotal
    };
  } catch (error) {
    console.error('Error processing section.json:', error);
    throw new Error('Failed to convert section data due to invalid JSON or structure');
  }
};