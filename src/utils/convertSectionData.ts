import sectionData from '@/data/section.json';

// Type definitions for the multi-level hierarchical structure
interface HierarchyNode {
  id: string;
  name: string;
  hours: number | null;
  children: HierarchyNode[] | null;
}

interface ProjectData {
  project: {
    title: string;
    grandTotal: number;
    sections: Record<string, HierarchyNode>;
  };
}

export const convertSectionToData = () => {
  try {
    const typedSectionData = sectionData as ProjectData;
    const sections = [];
    let grandTotal = 0;
    let number = 0;

    // Helper function to recursively traverse the hierarchy and collect leaf nodes with hours
    // Starting from section children, so level1 = first child, level2 = second child, etc.
    const collectLeafNodes = (node: HierarchyNode, level1Name = '', level2Name = '', level3Name = '', level4Name = '', currentLevel = 0): any[] => {
      const items = [];

      if (node.children && node.children.length > 0) {
        // This is not a leaf node, traverse children
        for (const child of node.children) {
          let childItems: any[];
          if (currentLevel === 0) {
            // First level children become level1
            childItems = collectLeafNodes(child, child.name, '', '', '', currentLevel + 1);
          } else if (currentLevel === 1) {
            // Second level children become level2
            childItems = collectLeafNodes(child, level1Name, child.name, '', '', currentLevel + 1);
          } else if (currentLevel === 2) {
            // Third level children become level3
            childItems = collectLeafNodes(child, level1Name, level2Name, child.name, '', currentLevel + 1);
          } else if (currentLevel === 3) {
            // Fourth level children become level4
            childItems = collectLeafNodes(child, level1Name, level2Name, level3Name, child.name, currentLevel + 1);
          } else {
            // Beyond level4, keep the same level assignments
            childItems = collectLeafNodes(child, level1Name, level2Name, level3Name, level4Name, currentLevel + 1);
          }
          items.push(...childItems);
        }
      } else if (node.hours !== null && node.hours !== undefined) {
        // This is a leaf node with hours, create an item
        number = number + 1;

        const hours = node.hours;
        const time = hours / 8; // 1 day = 8 hours
        const price = hours * 12.5; // Price = hours * 12.5 USD

        items.push({
          no: number,
          level1: level1Name,
          level2: level2Name || node.name,
          level3: level3Name,
          level4: level4Name,
          hours: hours,
          time: time,
          price: price,
          note: ''
        });
      }

      return items;
    };

    // Process each section in the project.sections
    for (const [, sectionValue] of Object.entries(typedSectionData.project.sections)) {
      const sectionItems = collectLeafNodes(sectionValue, '', '', '', '', 0);
      let sectionTotalPrice = 0;

      // Calculate section total
      for (const item of sectionItems) {
        sectionTotalPrice += item.price;
      }

      // Add section data to sections array
      sections.push({
        title: sectionValue.name,
        totalPrice: sectionTotalPrice,
        items: sectionItems
      });

      // Add section total to grand total
      grandTotal += sectionTotalPrice;
    }

    return {
      sections,
      grandTotal
    };
  } catch (error) {
    console.error('Error processing section.json:', error);
    throw new Error('Failed to convert section data due to invalid JSON or structure');
  }
};