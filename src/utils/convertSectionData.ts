import sectionData from '@/data/section.json';

// Type definitions for the section data structure
interface TaskDetails {
  level1: string;
  level2: string;
  level3: string;
  level4: string;
}

interface TaskResources {
  hours: number;
  time?: number; // Optional, calculated
  price?: number; // Optional, calculated
}

interface Task {
  no: number;
  title: string;
  details: TaskDetails;
  resources: TaskResources;
  note: string;
}

interface Category {
  id: string;
  name: string;
  tasks: Record<string, Task>;
}

interface Section {
  id: string;
  title: string;
  categories: Record<string, Category>;
}

interface ProjectData {
  project: {
    title: string;
    grandTotal: number;
    sections: Record<string, Section>;
  };
}

export const convertSectionToData = () => {
  try {
    const typedSectionData = sectionData as ProjectData;
    const sections = [];
    let grandTotal = 0;

    let number = 0;

    // Iterate through each section in the project
    for (const [sectionKey, sectionValue] of Object.entries(typedSectionData.project.sections)) {
      const sectionItems = [];
      let sectionTotalPrice = 0;

      // Validate section structure
      if (!sectionValue.categories) {
        console.warn(`Section ${sectionKey} is missing categories`);
        continue;
      }

      // Iterate through categories in each section
      for (const [categoryKey, categoryValue] of Object.entries(sectionValue.categories)) {
        // Validate category structure
        if (!categoryValue.tasks) {
          console.warn(`Category ${categoryKey} in section ${sectionKey} is missing tasks`);
          continue;
        }

        // Iterate through tasks in each category
        for (const [taskKey, taskValue] of Object.entries(categoryValue.tasks)) {
          // Validate task structure
          if (!taskValue.resources || typeof taskValue.resources.hours !== 'number') {
            console.warn(`Task ${taskKey} in category ${categoryKey} has invalid resources`);
            continue;
          }

          // Calculate time (in days) and price based on hours
          const hours = taskValue.resources.hours;
          const time = hours / 8; // 1 day = 8 hours
          const price = hours * 12.5; // Price = hours * 12.5 USD

          number = number + 1;

          // Add task data to sectionItems
          sectionItems.push({
            no: number,
            level1: taskValue.details.level1 || '',
            level2: taskValue.details.level2 || '',
            level3: taskValue.details.level3 || '',
            level4: taskValue.details.level4 || '',
            hours: taskValue.resources.hours,
            time: time,
            price: price,
            note: taskValue.note || ''
          });

          // Add task price to section total
          sectionTotalPrice += price;
        }
      }

      // Add section data to sections array
      sections.push({
        title: sectionValue.title || sectionKey,
        totalPrice: sectionTotalPrice,
        items: sectionItems
      });

      // Add section total to grand total
      grandTotal += sectionTotalPrice;
    }

    return {
      sections,
      grandTotal
    };
  } catch (error) {
    console.error('Error processing section.json:', error);
    throw new Error('Failed to convert section data due to invalid JSON or structure');
  }
};