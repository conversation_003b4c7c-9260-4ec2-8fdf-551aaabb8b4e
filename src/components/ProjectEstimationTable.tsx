
import {
  Table,
  TableBody,
  <PERSON><PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { convertSectionToData } from "@/utils/convertSectionData";
import tableConfig from "@/data/table.json";

const ProjectEstimationTable = () => {
  const tableData = convertSectionToData();

  return (
    <div className={tableConfig.styling.container.classes}>
      <div className={tableConfig.styling.tableWrapper.classes}>
        <Table>
          <TableHeader>
            <TableRow className={`${tableConfig.colors.header.background} ${tableConfig.colors.header.border}`}>
              <TableHead className={`${tableConfig.styling.header.cell.commonClasses} ${tableConfig.layout.columnWidths.no} ${tableConfig.layout.textAlignment.no}`}>
                {tableConfig.text.headers.no}
              </TableHead>
              <TableHead className={`${tableConfig.styling.header.cell.commonClasses} ${tableConfig.layout.columnWidths.level1}`}>
                {tableConfig.text.headers.level1}
              </TableHead>
              <TableHead className={`${tableConfig.styling.header.cell.commonClasses} ${tableConfig.layout.columnWidths.level2}`}>
                {tableConfig.text.headers.level2}
              </TableHead>
              <TableHead className={`${tableConfig.styling.header.cell.commonClasses} ${tableConfig.layout.columnWidths.level3}`}>
                {tableConfig.text.headers.level3}
              </TableHead>
              <TableHead className={`${tableConfig.styling.header.cell.commonClasses} ${tableConfig.layout.columnWidths.level4}`}>
                {tableConfig.text.headers.level4}
              </TableHead>
              <TableHead className={`${tableConfig.styling.header.cell.commonClasses} ${tableConfig.layout.columnWidths.hours} ${tableConfig.layout.textAlignment.hours}`}>
                {tableConfig.text.headers.hours}
              </TableHead>
              <TableHead className={`${tableConfig.styling.header.cell.commonClasses} ${tableConfig.layout.columnWidths.time} ${tableConfig.layout.textAlignment.time}`}>
                {tableConfig.text.headers.time}
              </TableHead>
              <TableHead className={`${tableConfig.styling.header.cell.commonClasses} ${tableConfig.layout.columnWidths.price} ${tableConfig.layout.textAlignment.price}`}>
                {tableConfig.text.headers.price}
              </TableHead>
              <TableHead className={`${tableConfig.styling.header.cell.classes} ${tableConfig.layout.columnWidths.note}`}>
                {tableConfig.text.headers.note}
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {tableData.sections.map((section, sectionIndex) => (
              <>
                {/* Section Header */}
                <TableRow key={`section-${sectionIndex}`} className={`${tableConfig.colors.sectionHeader.background} ${tableConfig.colors.sectionHeader.text}`}>
                  <TableCell colSpan={8} className={`font-bold text-left ${tableConfig.styling.cells.default}`}>
                    {section.title}
                  </TableCell>
                  <TableCell className="font-bold text-right">
                    {tableConfig.text.formatting.price}{section.totalPrice}
                  </TableCell>
                </TableRow>
                
                {/* Section Items */}
                {section.items.map((item, itemIndex) => {
                  // Check if this is the first occurrence of this level1 value
                  const isFirstOfGroup = itemIndex === 0 || section.items[itemIndex - 1].level1 !== item.level1;
                  // Count how many consecutive items have the same level1
                  let rowSpan = 1;
                  if (isFirstOfGroup) {
                    for (let i = itemIndex + 1; i < section.items.length; i++) {
                      if (section.items[i].level1 === item.level1) {
                        rowSpan++;
                      } else {
                        break;
                      }
                    }
                  }

                  return (
                    <TableRow key={`item-${item.no}`} className={`${tableConfig.colors.itemRow.border} ${tableConfig.colors.itemRow.background}`}>
                      <TableCell className={`${tableConfig.styling.cells.center}`}>
                        {item.no}
                      </TableCell>
                      {isFirstOfGroup ? (
                        <TableCell
                          className={tableConfig.styling.cells.default}
                          rowSpan={rowSpan}
                        >
                          {item.level1}
                        </TableCell>
                      ) : null}
                      <TableCell className={tableConfig.styling.cells.default}>
                        {item.level2.includes('\n') ? (
                          <div>
                            {item.level2.split('\n').map((line: string, i: number) => (
                              <div key={i} className={
                                tableConfig.special.level2Formatting.detectPatterns.some(pattern =>
                                  line.trim().startsWith(pattern)
                                ) ? tableConfig.special.level2Formatting.indentClasses : ''
                              }>
                                {line.trim()}
                              </div>
                            ))}
                          </div>
                        ) : (
                          item.level2
                        )}
                      </TableCell>
                      <TableCell className={tableConfig.styling.cells.default}>{item.level3}</TableCell>
                      <TableCell className={tableConfig.styling.cells.default}>{item.level4}</TableCell>
                      <TableCell className={tableConfig.styling.cells.center}>
                        {item.hours}
                      </TableCell>
                      <TableCell className={tableConfig.styling.cells.center}>
                        {item.time}
                      </TableCell>
                      <TableCell className={tableConfig.styling.cells.right}>
                        {tableConfig.text.formatting.price}{item.price}
                      </TableCell>
                      <TableCell className="p-4">{item.note}</TableCell>
                    </TableRow>
                  );
                })}
              </>
            ))}

            {/* Total Row */}
            <TableRow className={`${tableConfig.colors.totalRow.background} ${tableConfig.colors.totalRow.border} font-bold`}>
              <TableCell colSpan={8} className={tableConfig.styling.cells.totalCenter}>
                {tableConfig.text.headers.total}
              </TableCell>
              <TableCell className={tableConfig.styling.cells.totalRight}>
                {tableConfig.text.formatting.price}{tableData.grandTotal}
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default ProjectEstimationTable;
